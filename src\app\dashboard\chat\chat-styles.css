/* Modern Chat Interface Styles with Application Color Scheme */
/* Colors: #133157 (primary), #febd49 (accent), #e0e0e0 (secondary), white (background) */

/* Enhanced styles for chat bubbles in light mode */
.message-bubble-user {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1.5px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.08), 0 1px 4px rgba(19, 49, 87, 0.06);
  border-radius: 20px 20px 20px 6px;
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  max-width: 85%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: relative;
  text-align: left;
  align-self: flex-start;
}

.message-bubble-user:hover {
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.12), 0 2px 6px rgba(19, 49, 87, 0.08);
  transform: translateY(-1px);
}

.message-bubble-agent {
  background: linear-gradient(135deg, #133157 0%, #1a4269 100%);
  border: 1.5px solid rgba(19, 49, 87, 0.2);
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.15), 0 1px 4px rgba(19, 49, 87, 0.1);
  border-radius: 20px 20px 6px 20px;
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  max-width: 85%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: relative;
  text-align: left;
  align-self: flex-end;
}

.message-bubble-agent:hover {
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.2), 0 2px 6px rgba(19, 49, 87, 0.15);
  transform: translateY(-1px);
}

/* Enhanced styles for chat bubbles in dark mode */
.dark .message-bubble-user {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
  border: 1.5px solid rgba(55, 65, 81, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
}

.dark .message-bubble-agent {
  background: linear-gradient(135deg, #133157 0%, #0f2643 100%);
  border: 1.5px solid rgba(19, 49, 87, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25), 0 1px 4px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
}

/* Avatar styles */
.avatar-user {
  background: linear-gradient(135deg, #e0e0e0 0%, #d1d5db 100%);
  color: #133157;
  font-weight: 600;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(19, 49, 87, 0.15);
}

.avatar-agent {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  color: #133157;
  font-weight: 700;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(254, 189, 73, 0.3);
}

.dark .avatar-user {
  border-color: rgba(31, 41, 55, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .avatar-agent {
  border-color: rgba(31, 41, 55, 1);
  box-shadow: 0 2px 8px rgba(254, 189, 73, 0.2);
}

/* Enhanced attachment card styles */
.attachment-card {
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.attachment-card:hover {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.attachment-card .file-icon {
  transition: transform 0.2s ease;
}

.attachment-card:hover .file-icon {
  transform: scale(1.1);
}

/* File type icons styling */
.file-icon {
  color: #133157;
  transition: color 0.2s ease;
}

.attachment-card:hover .file-icon {
  color: #febd49;
}

.dark .file-icon {
  color: #e0e0e0;
}

.dark .attachment-card:hover .file-icon {
  color: #febd49;
}

/* Enhanced websocket indicator styles with application colors */
.ws-connection-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.ws-connection-indicator-connected {
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.ws-connection-indicator-connecting,
.ws-connection-indicator-reconnecting {
  color: #febd49;
  background: rgba(254, 189, 73, 0.1);
  border: 1px solid rgba(254, 189, 73, 0.2);
  animation: pulse-glow 2s infinite;
}

.ws-connection-indicator-disconnected {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Modern custom scrollbar with application colors */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #133157 0%, #1a4269 100%);
  border-radius: 6px;
  border: 1px solid rgba(224, 224, 224, 0.3);
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  border-color: rgba(254, 189, 73, 0.5);
  transform: scale(1.1);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(19, 49, 87, 0.8) 0%, rgba(26, 66, 105, 0.8) 100%);
  border-color: rgba(55, 65, 81, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  border-color: rgba(254, 189, 73, 0.4);
}

/* Enhanced animations with modern easing */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.98);
  }
}

@keyframes typing-dots {
  0%, 20% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  80%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.typing-dot {
  animation: typing-dots 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0ms; }
.typing-dot:nth-child(2) { animation-delay: 200ms; }
.typing-dot:nth-child(3) { animation-delay: 400ms; }

/* Message status icons animation */
.message-status-icon {
  transition: all 0.2s ease;
}

.message-status-sending {
  animation: pulse 1s infinite;
  color: #febd49;
}

.message-status-sent {
  color: #e0e0e0;
}

.message-status-delivered {
  color: #133157;
}

.message-status-read {
  color: #10B981;
}

/* Enhanced search highlighting with application colors */
.search-highlight {
  animation: highlight-pulse 1.5s ease-in-out;
  background: linear-gradient(135deg, rgba(254, 189, 73, 0.2) 0%, rgba(245, 158, 11, 0.2) 100%);
  border-radius: 4px;
  padding: 1px 2px;
}

.search-result {
  position: relative;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.current-search-result {
  transform: scale(1.02);
  animation: highlight-pulse 2s infinite;
}

.current-search-result::before {
  content: '';
  position: absolute;
  top: -6px;
  bottom: -6px;
  left: -6px;
  right: -6px;
  border: 2px solid #febd49;
  border-radius: 12px;
  pointer-events: none;
  animation: border-pulse 1.5s infinite;
  z-index: -1;
  background: rgba(254, 189, 73, 0.05);
}

@keyframes highlight-pulse {
  0% { 
    background: transparent; 
    transform: scale(1);
  }
  30% { 
    background: linear-gradient(135deg, rgba(254, 189, 73, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%);
    transform: scale(1.05);
  }
  70% { 
    background: linear-gradient(135deg, rgba(254, 189, 73, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%);
    transform: scale(1.05);
  }
  100% { 
    background: transparent; 
    transform: scale(1);
  }
}

@keyframes border-pulse {
  0% { 
    border-color: rgba(254, 189, 73, 0.4);
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0.3);
  }
  50% { 
    border-color: rgba(254, 189, 73, 0.8);
    box-shadow: 0 0 0 4px rgba(254, 189, 73, 0.1);
  }
  100% { 
    border-color: rgba(254, 189, 73, 0.4);
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0.3);
  }
}

/* Search highlight effects */
.search-result {
  animation: pulse 2s infinite;
}

.current-search-result {
  animation: highlight-pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes highlight-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(254, 189, 73, 0);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(254, 189, 73, 0.4);
  }
}

/* Message grouping and conversation flow */
.message-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 20px;
}

.message-group-user {
  align-items: flex-start;
}

.message-group-agent {
  align-items: flex-end;
}

.message-container {
  display: flex;
  max-width: 100%;
  position: relative;
  transition: all 0.3s ease;
  margin-bottom: 16px;
  padding: 0 8px;
}

.message-container-user {
  justify-content: flex-start;
  align-items: flex-start;
}

.message-container-agent {
  justify-content: flex-end;
  align-items: flex-start;
}

/* Responsive message spacing and alignment */
@media (min-width: 768px) {
  .message-container {
    padding: 0 16px;
    margin-bottom: 18px;
  }
}

@media (min-width: 1024px) {
  .message-container {
    padding: 0 24px;
    margin-bottom: 20px;
  }
}

/* Consistent spacing for all messages with WhatsApp-like layout */

/* Message timestamp styling */
.message-timestamp {
  font-size: 0.7rem;
  color: rgba(19, 49, 87, 0.5);
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-container:hover .message-timestamp {
  opacity: 1;
}

.dark .message-timestamp {
  color: rgba(224, 224, 224, 0.6);
}

/* Message input area enhancements */
.message-input-container {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid #e0e0e0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(10px);
  padding: 16px 20px;
}

.dark .message-input-container {
  border-top: 1px solid rgba(55, 65, 81, 0.5);
  background: linear-gradient(to bottom, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 1) 100%);
}

.message-textarea {
  position: relative;
  width: 100%;
  min-height: 48px;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px 16px;
  padding-right: 48px;
  background-color: white;
  border: 2px solid #e0e0e0;
  border-radius: 18px;
  resize: none;
  font-size: 0.9rem;
  line-height: 1.5;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(19, 49, 87, 0.06);
}

.message-textarea:focus {
  border-color: #133157;
  outline: none;
  box-shadow: 0 0 0 3px rgba(19, 49, 87, 0.15);
}

.dark .message-textarea {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: rgba(55, 65, 81, 0.6);
  color: white;
}

.dark .message-textarea:focus {
  border-color: #febd49;
  box-shadow: 0 0 0 3px rgba(254, 189, 73, 0.2);
}

/* Character counter */
.character-counter {
  font-size: 0.75rem;
  color: rgba(19, 49, 87, 0.5);
  display: flex;
  justify-content: space-between;
  margin-top: 6px;
  padding: 0 4px;
  transition: color 0.2s ease;
}

.character-counter-warning {
  color: #febd49;
}

.character-counter-danger {
  color: #ef4444;
}

/* Attachment preview area */
.attachment-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.attachment-preview {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(19, 49, 87, 0.05);
  border: 1px solid rgba(19, 49, 87, 0.1);
  position: relative;
  transition: all 0.2s ease;
}

.attachment-preview:hover {
  background: rgba(19, 49, 87, 0.08);
}

.attachment-preview-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #f43f5e;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  border: 1px solid rgba(244, 63, 94, 0.2);
}

.attachment-preview:hover .attachment-preview-close {
  opacity: 1;
}

.attachment-preview-close:hover {
  background: #f43f5e;
  color: white;
  transform: scale(1.1);
}

/* Scroll to bottom button */
.scroll-to-bottom-button {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #133157;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.2);
  z-index: 5;
  transition: all 0.2s ease;
}

.scroll-to-bottom-button:hover {
  background: #1a4269;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(19, 49, 87, 0.3);
}

/* File drag and drop styles */
.drag-active {
  border: 2px dashed #133157;
  background-color: rgba(19, 49, 87, 0.05);
}





/* File drag and drop styles */
.drag-active {
  border: 2px dashed #133157;
  background-color: rgba(19, 49, 87, 0.05);
}